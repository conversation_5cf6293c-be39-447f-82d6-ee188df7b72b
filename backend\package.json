{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "nodemon --watch src --ext ts --exec ts-node src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "start": "ts-node src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/uuid": "^10.0.0", "cors": "^2.8.5", "express": "^5.1.0", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.24", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}