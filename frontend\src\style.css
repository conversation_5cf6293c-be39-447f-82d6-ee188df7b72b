:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  /* color: rgba(255, 255, 255, 0.87);
     background-color: #242424; */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 主题变量 */
  --bg-color: #ffffff;
  --text-color: #213547;
  --card-bg: #f9f9f9;
  --button-bg: #f9f9f9;
}

html.dark-mode {
  --bg-color: #242424;
  --text-color: rgba(255,255,255,0.87);
  --card-bg: #333333;
  --button-bg: #1a1a1a;
}

body {
  margin: 0;
  display: block;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: left;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Element Plus 组件覆盖 */
.el-card {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
}
.el-button {
  background-color: var(--button-bg) !important;
  color: var(--text-color) !important;
}
.el-input, .el-input-number {
  background-color: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 移动端卡片与按钮间距优化 */
@media (max-width: 768px) {
  .el-card {
    margin: 0.5rem !important;
  }
  .el-button {
    margin: 0.5rem 0 !important;
    width: 100%;
  }
}
